#!/usr/bin/env node

/**
 * Upload Florida PDF template to PDF.co built-in storage
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

const PDF_CO_API_KEY = 'ilyassrachouady8@gmail.com_rrrGTEibjUr1XVIJUwrss3FaSDC5IwTeWBEkUxskcbgIidBRbPpMQn8126v3CdNx';

console.log('📤 Uploading Florida PDF template to PDF.co storage...\n');

// Read the PDF file
const pdfPath = path.join(__dirname, 'public', 'Florida-As_Is-Purchase-Agreement.pdf');

if (!fs.existsSync(pdfPath)) {
  console.error('❌ PDF file not found:', pdfPath);
  console.log('Available files in public/:');
  fs.readdirSync(path.join(__dirname, 'public')).forEach(file => {
    if (file.endsWith('.pdf')) {
      console.log('  📄', file);
    }
  });
  process.exit(1);
}

// Read PDF as base64
const pdfBuffer = fs.readFileSync(pdfPath);
const pdfBase64 = pdfBuffer.toString('base64');

console.log('✅ PDF file loaded:', pdfPath);
console.log('📊 File size:', pdfBuffer.length, 'bytes');

// Prepare upload payload
const payload = {
  name: 'Florida-As_Is-Purchase-Agreement.pdf',
  data: pdfBase64
};

const jsonPayload = JSON.stringify(payload);

// Upload to PDF.co
const options = {
  hostname: 'api.pdf.co',
  port: 443,
  path: '/v1/file/upload',
  method: 'POST',
  headers: {
    'x-api-key': PDF_CO_API_KEY,
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(jsonPayload)
  }
};

console.log('🚀 Uploading to PDF.co...');

const req = https.request(options, (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      
      if (response.error === false) {
        console.log('✅ SUCCESS! PDF uploaded to PDF.co');
        console.log('📄 File URL:', response.url);
        console.log('🔗 Permanent URL:', response.url);
        
        console.log('\n🔧 Next Steps:');
        console.log('1. Copy this URL:', response.url);
        console.log('2. Update TEMPLATE_PDF_URL in server/pdfCoGenerator.cjs');
        console.log('3. Restart your server');
        console.log('4. Test PDF generation');
        
        // Show the exact code to update
        console.log('\n📝 Update this line in server/pdfCoGenerator.cjs:');
        console.log(`const TEMPLATE_PDF_URL = '${response.url}';`);
        
      } else {
        console.log('❌ Upload failed:', response.message);
      }
    } catch (parseError) {
      console.error('❌ Failed to parse response:', parseError);
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Upload failed:', error.message);
});

req.write(jsonPayload);
req.end();
