const express = require('express');
const puppeteer = require('puppeteer');
const handlebars = require('handlebars');
const fs = require('fs').promises;
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Handlebars helpers for formatting
handlebars.registerHelper('currency', function(value) {
  if (!value) return '';
  const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]/g, '')) : value;
  if (isNaN(numValue)) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(numValue);
});

handlebars.registerHelper('formatDate', function(value) {
  if (!value) return '';
  const date = new Date(value);
  if (isNaN(date.getTime())) return '';
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
});

handlebars.registerHelper('checkbox', function(value) {
  return value ? '✓' : '';
});

/**
 * Process form data for HTML template
 */
function processFormDataForTemplate(formData) {
  console.log('🔄 Processing form data for HTML template...');

  const processedData = {
    // Property Information
    property_address: formData.propertyAddress || formData.address || '',
    county: formData.county || '',
    tax_id: formData.taxId || formData.propertyTaxId || '',
    legal_description: formData.legalDescription || formData.legalDescriptionLine1 || '',
    
    // Party Information
    seller_name: formData.sellerName || formData.SELLER || '',
    buyer_name: formData.buyerName || formData.BUYER || '',
    
    // Financial Information
    purchase_price: formatCurrency(formData.purchasePrice),
    initial_deposit: formatCurrency(formData.depositAmount || formData.initialDeposit),
    balance_to_close: formatCurrency(calculateBalance(formData)),
    
    // Dates
    closing_date: formatDate(formData.closingDate),
    effective_date: formatDate(formData.effectiveDate || new Date()),
    generation_date: formatDate(new Date()),
    
    // Financing Options
    cash_purchase: formData.cash ? '✓' : '',
    financing_required: formData.financing ? '✓' : '',
    conventional_loan: formData.conventional ? '✓' : '',
    fha_loan: formData.fha ? '✓' : '',
    va_loan: formData.va ? '✓' : '',
    
    // Closing Options
    seller_pays_closing: formData.sellerDesignatesClosing ? '✓' : '',
    buyer_pays_closing: !formData.sellerDesignatesClosing ? '✓' : '',
    
    // Escrow Information
    escrow_agent_name: formData.escrowAgentName || 'Professional Title Company',
    escrow_agent_address: formData.escrowAgentAddress || '123 Main Street, Miami, FL 33101',
    escrow_agent_phone: formData.escrowAgentPhone || '(*************',
    escrow_agent_email: formData.escrowAgentEmail || '<EMAIL>',
    
    // Personal Property
    personal_property: formData.personalProperty || 'Range/oven, refrigerator, dishwasher, disposal, ceiling fans, light fixtures, drapery rods and draperies, blinds, smoke detectors, garage door opener',
    excluded_items: formData.excludedItems || 'None',
    
    // Additional Terms
    additional_terms: formData.additionalTerms || 'Property sold in AS-IS condition. Buyer acknowledges receipt of property disclosure.',
    
    // Signatures
    buyer_initials: formData.buyerInitials || '',
    seller_initials: formData.sellerInitials || '',
  };

  console.log('✅ Processed data for template:', Object.keys(processedData).length, 'fields');
  return processedData;
}

/**
 * Helper functions
 */
function formatCurrency(value) {
  if (!value) return '';
  const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]/g, '')) : value;
  if (isNaN(numValue)) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(numValue);
}

function formatDate(value) {
  if (!value) return '';
  const date = new Date(value);
  if (isNaN(date.getTime())) return '';
  return date.toLocaleDateString('en-US');
}

function calculateBalance(formData) {
  const purchasePrice = parseFloat(formData.purchasePrice) || 0;
  const deposit = parseFloat(formData.depositAmount) || 0;
  return purchasePrice - deposit;
}

/**
 * Generate PDF from HTML template
 */
async function generatePdfFromHtml(formData) {
  console.log('🎯 Starting HTML-to-PDF generation...');
  
  let browser;
  try {
    // Step 1: Load HTML template
    const templatePath = path.join(__dirname, '..', 'public', 'template.html');
    const templateContent = await fs.readFile(templatePath, 'utf8');
    console.log('✅ HTML template loaded');

    // Step 2: Process form data
    const processedData = processFormDataForTemplate(formData);
    console.log('✅ Form data processed');

    // Step 3: Compile template
    const template = handlebars.compile(templateContent);
    const filledHtml = template(processedData);
    console.log('✅ HTML template compiled');

    // Step 4: Generate PDF with Puppeteer
    console.log('🚀 Launching Puppeteer...');
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--font-render-hinting=none'
      ]
    });

    const page = await browser.newPage();
    
    // Set content and wait for everything to load
    await page.setContent(filledHtml, { 
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Generate PDF with professional settings
    const pdfBuffer = await page.pdf({
      format: 'A4',
      margin: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in'
      },
      printBackground: true,
      preferCSSPageSize: true,
      displayHeaderFooter: false
    });

    console.log('✅ PDF generated successfully');
    console.log('📄 PDF size:', pdfBuffer.length, 'bytes');

    return {
      success: true,
      pdfBuffer,
      filledHtml // For debugging
    };

  } catch (error) {
    console.error('❌ PDF generation failed:', error);
    return {
      success: false,
      error: error.message
    };
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// API Endpoints

/**
 * Generate PDF - Main endpoint
 */
app.post('/generate-pdf', async (req, res) => {
  console.log('\n🎯 === PDF GENERATION REQUEST ===');
  console.log('📊 Form data received:', Object.keys(req.body).length, 'fields');

  try {
    const result = await generatePdfFromHtml(req.body);

    if (result.success) {
      console.log('✅ PDF generation successful');
      
      // Set headers for PDF download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="Florida-Purchase-Agreement.pdf"');
      res.setHeader('Content-Length', result.pdfBuffer.length);
      
      // Send PDF
      res.send(result.pdfBuffer);
      
      console.log('📤 PDF sent to client');
    } else {
      console.error('❌ PDF generation failed:', result.error);
      res.status(500).json({
        error: 'PDF generation failed',
        details: result.error
      });
    }

  } catch (error) {
    console.error('❌ Server error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * Preview HTML - Debug endpoint
 */
app.post('/preview-html', async (req, res) => {
  console.log('\n🔍 === HTML PREVIEW REQUEST ===');

  try {
    // Load template
    const templatePath = path.join(__dirname, '..', 'public', 'template.html');
    const templateContent = await fs.readFile(templatePath, 'utf8');

    // Process data and fill template
    const processedData = processFormDataForTemplate(req.body);
    const template = handlebars.compile(templateContent);
    const filledHtml = template(processedData);

    // Return HTML for preview
    res.setHeader('Content-Type', 'text/html');
    res.send(filledHtml);

    console.log('✅ HTML preview generated');

  } catch (error) {
    console.error('❌ HTML preview error:', error);
    res.status(500).json({
      error: 'HTML preview failed',
      details: error.message
    });
  }
});

/**
 * Health check
 */
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Clean HTML-to-PDF Generator',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

// Start server
app.listen(PORT, () => {
  console.log('\n🚀 === CLEAN PDF GENERATOR STARTED ===');
  console.log(`📡 Server running on port ${PORT}`);
  console.log(`🎯 Generate PDF: POST http://localhost:${PORT}/generate-pdf`);
  console.log(`🔍 Preview HTML: POST http://localhost:${PORT}/preview-html`);
  console.log(`❤️ Health Check: GET http://localhost:${PORT}/health`);
  console.log('✅ Ready to generate perfect PDFs!\n');
});

module.exports = { generatePdfFromHtml, processFormDataForTemplate };
