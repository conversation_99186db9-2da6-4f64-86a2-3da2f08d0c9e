const express = require('express');
const { PDFDocument } = require('pdf-lib');
const fs = require('fs').promises;
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3001;

console.log('🚀 Starting FloridaTemplate PDF Generator Server...');

// Middleware
app.use(cors());
app.use(express.json());

// FloridaTemplate mapping function (simplified version for server)
function mapFormDataToFloridaTemplate(formData) {
  const mapping = {};

  // Handle both structured FormData and flat form data
  if (formData.propertyInfo) {
    // Structured FormData from React app
    mapping["'(Text_1)'"] = formData.propertyInfo.sellerName || '';
    mapping["'(Text_2)'"] = formData.propertyInfo.buyerName || '';
    mapping["'(Text_3)'"] = formData.propertyInfo.streetAddress || '';
    mapping["'(Text_4)'"] = formData.propertyInfo.county || '';
    mapping["'(Text_5)'"] = formData.propertyInfo.propertyTaxId || '';
    mapping["'(Text_6)'"] = formData.propertyInfo.legalDescription1 || '';
    mapping["'(Text_7)'"] = formData.propertyInfo.legalDescription2 || '';
    mapping["'(Text_9)'"] = formData.propertyInfo.personalPropertyIncluded || '';
    mapping["'(Text_10)'"] = formData.propertyInfo.additionalPersonalProperty || '';
    mapping["'(Text_11)'"] = formData.propertyInfo.itemsExcluded || '';

    // Financial info
    mapping["'(Number_1)'"] = formData.financialInfo.purchasePrice?.toString() || '';
    mapping["'(Number_2)'"] = formData.financialInfo.initialDeposit?.toString() || '';
    mapping["'(Number_3)'"] = formData.financialInfo.balanceToClose?.toString() || '';

    // Payment method checkboxes
    mapping["'(Checkbox_8)'"] = formData.paymentMethod.paymentType === 'cash';
    mapping["'(Checkbox_9)'"] = formData.paymentMethod.loanType === 'conventional';
    mapping["'(Checkbox_10)'"] = formData.paymentMethod.loanType === 'fha';
    mapping["'(Checkbox_11)'"] = formData.paymentMethod.loanType === 'va';

    // Dates
    mapping["'(Date_1)'"] = formData.partyDetails.contractDate || '';

    // Initials - populate multiple fields with same initials
    const sellerInitials = formData.partyDetails.sellerInitials || '';
    const buyerInitials = formData.partyDetails.buyerInitials || '';
    for (let i = 1; i <= 36; i++) {
      const isSellerField = i % 2 === 1; // Odd numbers for seller, even for buyer
      mapping[`'(Initials_${i})'`] = isSellerField ? sellerInitials : buyerInitials;
    }
  }

  return mapping;
}

/**
 * Process form data for HTML template
 */
function processFormDataForTemplate(formData) {
  console.log('🔄 Processing form data for HTML template...');

  const processedData = {
    // Property Information
    property_address: formData.propertyAddress || formData.address || '',
    county: formData.county || '',
    tax_id: formData.taxId || formData.propertyTaxId || '',
    legal_description: formData.legalDescription || formData.legalDescriptionLine1 || '',
    
    // Party Information
    seller_name: formData.sellerName || formData.SELLER || '',
    buyer_name: formData.buyerName || formData.BUYER || '',
    
    // Financial Information
    purchase_price: formatCurrency(formData.purchasePrice),
    initial_deposit: formatCurrency(formData.depositAmount || formData.initialDeposit),
    balance_to_close: formatCurrency(calculateBalance(formData)),
    
    // Dates
    closing_date: formatDate(formData.closingDate),
    effective_date: formatDate(formData.effectiveDate || new Date()),
    generation_date: formatDate(new Date()),
    
    // Financing Options
    cash_purchase: formData.cash ? '✓' : '',
    financing_required: formData.financing ? '✓' : '',
    conventional_loan: formData.conventional ? '✓' : '',
    fha_loan: formData.fha ? '✓' : '',
    va_loan: formData.va ? '✓' : '',
    
    // Closing Options
    seller_pays_closing: formData.sellerDesignatesClosing ? '✓' : '',
    buyer_pays_closing: !formData.sellerDesignatesClosing ? '✓' : '',
    
    // Escrow Information
    escrow_agent_name: formData.escrowAgentName || 'Professional Title Company',
    escrow_agent_address: formData.escrowAgentAddress || '123 Main Street, Miami, FL 33101',
    escrow_agent_phone: formData.escrowAgentPhone || '(*************',
    escrow_agent_email: formData.escrowAgentEmail || '<EMAIL>',
    
    // Personal Property
    personal_property: formData.personalProperty || 'Range/oven, refrigerator, dishwasher, disposal, ceiling fans, light fixtures, drapery rods and draperies, blinds, smoke detectors, garage door opener',
    excluded_items: formData.excludedItems || 'None',
    
    // Additional Terms
    additional_terms: formData.additionalTerms || 'Property sold in AS-IS condition. Buyer acknowledges receipt of property disclosure.',
    
    // Signatures
    buyer_initials: formData.buyerInitials || '',
    seller_initials: formData.sellerInitials || '',
  };

  console.log('✅ Processed data for template:', Object.keys(processedData).length, 'fields');
  return processedData;
}

/**
 * Helper functions
 */
function formatCurrency(value) {
  if (!value) return '';
  const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]/g, '')) : value;
  if (isNaN(numValue)) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(numValue);
}

function formatDate(value) {
  if (!value) return '';
  const date = new Date(value);
  if (isNaN(date.getTime())) return '';
  return date.toLocaleDateString('en-US');
}

function calculateBalance(formData) {
  const purchasePrice = parseFloat(formData.purchasePrice) || 0;
  const deposit = parseFloat(formData.depositAmount) || 0;
  return purchasePrice - deposit;
}

// API Endpoints

/**
 * Generate PDF using FloridaTemplate.pdf - Main endpoint
 */
app.post('/generate-pdf', async (req, res) => {
  console.log('\n🎯 === FLORIDA TEMPLATE PDF GENERATION REQUEST ===');
  console.log('📊 Form data received:', Object.keys(req.body).length, 'fields');

  try {
    // Use FloridaTemplate mapping
    const mappedFields = mapFormDataToFloridaTemplate(req.body);
    console.log(`✓ Mapped ${Object.keys(mappedFields).length} fields for FloridaTemplate.pdf`);

    // Load FloridaTemplate.pdf
    const templatePath = path.join(__dirname, '..', 'public', 'FloridaTemplate.pdf');
    console.log('Loading FloridaTemplate.pdf from:', templatePath);

    const existingPdfBytes = await fs.readFile(templatePath);
    console.log('✓ Template loaded, size:', existingPdfBytes.length, 'bytes');

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(existingPdfBytes);
    const form = pdfDoc.getForm();

    // Get all form fields
    const fields = form.getFields();
    console.log(`✓ Found ${fields.length} form fields in FloridaTemplate.pdf`);

    // Fill the form fields
    let filledCount = 0;
    Object.entries(mappedFields).forEach(([fieldName, value]) => {
      try {
        const field = form.getField(fieldName);

        if (field) {
          if (typeof value === 'boolean') {
            // Handle checkbox fields
            if (field.constructor.name === 'PDFCheckBox') {
              if (value) {
                field.check();
              } else {
                field.uncheck();
              }
              filledCount++;
            }
          } else if (value !== null && value !== undefined && value !== '') {
            // Handle text/number fields
            if (field.constructor.name === 'PDFTextField') {
              field.setText(String(value));
              filledCount++;
            }
          }
        }
      } catch (error) {
        console.warn(`⚠️ Could not fill field ${fieldName}:`, error.message);
      }
    });

    console.log(`✅ Successfully filled ${filledCount} fields`);

    // Generate the PDF
    const pdfBytes = await pdfDoc.save();
    console.log('✅ FloridaTemplate PDF generated successfully');
    console.log('📄 PDF size:', pdfBytes.length, 'bytes');

    // Set headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename="Florida-Purchase-Agreement-Filled.pdf"');
    res.setHeader('Content-Length', pdfBytes.length);

    // Send PDF
    res.send(Buffer.from(pdfBytes));

    console.log('📤 FloridaTemplate PDF sent to client');

  } catch (error) {
    console.error('❌ FloridaTemplate PDF generation failed:', error);
    res.status(500).json({
      error: 'FloridaTemplate PDF generation failed',
      details: error.message
    });
  }
});

/**
 * Preview HTML - Debug endpoint
 */
app.post('/preview-html', async (req, res) => {
  console.log('\n🔍 === HTML PREVIEW REQUEST ===');

  try {
    // Load template
    const templatePath = path.join(__dirname, '..', 'public', 'template.html');
    const templateContent = await fs.readFile(templatePath, 'utf8');

    // Process data and fill template
    const processedData = processFormDataForTemplate(req.body);
    const template = handlebars.compile(templateContent);
    const filledHtml = template(processedData);

    // Return HTML for preview
    res.setHeader('Content-Type', 'text/html');
    res.send(filledHtml);

    console.log('✅ HTML preview generated');

  } catch (error) {
    console.error('❌ HTML preview error:', error);
    res.status(500).json({
      error: 'HTML preview failed',
      details: error.message
    });
  }
});

/**
 * Health check
 */
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'FloridaTemplate PDF Generator',
    template: 'FloridaTemplate.pdf',
    timestamp: new Date().toISOString(),
    version: '3.0.0'
  });
});

// Start server
app.listen(PORT, () => {
  console.log('\n🚀 === FLORIDA TEMPLATE PDF GENERATOR STARTED ===');
  console.log(`📡 Server running on port ${PORT}`);
  console.log(`🎯 Generate PDF: POST http://localhost:${PORT}/generate-pdf`);
  console.log(`📄 Template: FloridaTemplate.pdf (fillable PDF)`);
  console.log(`❤️ Health Check: GET http://localhost:${PORT}/health`);
  console.log('✅ Ready to generate FloridaTemplate PDFs!\n');
});

module.exports = { mapFormDataToFloridaTemplate };
