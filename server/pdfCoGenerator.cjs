const express = require('express');
const https = require('https');
const fs = require('fs').promises;
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// PDF.co API Configuration
const PDF_CO_API_KEY = process.env.PDF_CO_API_KEY || 'ilyassrachouady8@gmail.com_rrrGTEibjUr1XVIJUwrss3FaSDC5IwTeWBEkUxskcbgIidBRbPpMQn8126v3CdNx';
const PDF_CO_BASE_URL = 'https://api.pdf.co/v1';

// Template PDF URL - Updated to use FloridaTemplate.pdf (fillable PDF)
const TEMPLATE_PDF_URL = 'https://formflorida.netlify.app/FloridaTemplate.pdf';

/**
 * Convert form data to PDF.co annotations format
 */
function convertFormDataToPdfCoAnnotations(formData) {
  console.log('🔄 Converting form data to PDF.co annotations...');

  // Based on the PDF.co documentation, we use annotations for text placement
  // These coordinates are for Florida AS-IS Purchase Agreement
  const annotations = [
    // Test annotation to verify PDF is working
    {
      text: "TEST - PDF.co Working!",
      x: 50,
      y: 800,
      size: 14,
      pages: "0",
      fontName: "Arial",
      color: "FF0000",
      type: "Text"
    },

    // Seller Information - Try multiple positions to find the right spot
    {
      text: `SELLER: ${formData.sellerName || 'TEST SELLER'}`,
      x: 100,
      y: 750,
      size: 12,
      pages: "0",
      fontName: "Times New Roman",
      type: "Text"
    },

    // Buyer Information - Try multiple positions
    {
      text: `BUYER: ${formData.buyerName || 'TEST BUYER'}`,
      x: 100,
      y: 700,
      size: 12,
      pages: "0",
      fontName: "Times New Roman",
      type: "Text"
    },
    
    // Property Address - Test position
    {
      text: `ADDRESS: ${formData.propertyAddress || 'TEST ADDRESS'}`,
      x: 100,
      y: 650,
      size: 11,
      pages: "0",
      fontName: "Times New Roman",
      type: "Text"
    },

    // Purchase Price - Make it very visible
    {
      text: `PRICE: ${formatCurrency(formData.purchasePrice) || '$500,000'}`,
      x: 100,
      y: 600,
      size: 14,
      pages: "0",
      fontName: "Times New Roman",
      fontBold: true,
      color: "0000FF",
      type: "Text"
    },

    // County and Tax ID
    {
      text: `COUNTY: ${formData.county || 'TEST COUNTY'}`,
      x: 100,
      y: 550,
      size: 11,
      pages: "0",
      fontName: "Times New Roman",
      type: "Text"
    },

    // Test different positions across the page
    {
      text: "POSITION TEST - TOP RIGHT",
      x: 400,
      y: 800,
      size: 10,
      pages: "0",
      fontName: "Arial",
      color: "00FF00",
      type: "Text"
    },
    
    // Test different areas of the page
    {
      text: "BOTTOM LEFT TEST",
      x: 50,
      y: 100,
      size: 12,
      pages: "0",
      fontName: "Arial",
      color: "FF00FF",
      type: "Text"
    },

    // Test center of page
    {
      text: "CENTER TEST",
      x: 300,
      y: 400,
      size: 12,
      pages: "0",
      fontName: "Arial",
      color: "FFFF00",
      type: "Text"
    }
  ];

  console.log('✅ Generated', annotations.length, 'annotations for PDF.co');
  console.log('📍 Note: Coordinates are estimated. Use PDF.co helper tool for exact positioning.');
  console.log('🔗 Helper tool: https://app.pdf.co/pdf-edit-add-helper');
  return annotations;
}

/**
 * Helper functions
 */
function formatCurrency(value) {
  if (!value) return '';
  const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]/g, '')) : value;
  if (isNaN(numValue)) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(numValue);
}

function formatDate(value) {
  if (!value) return '';
  const date = new Date(value);
  if (isNaN(date.getTime())) return '';
  return date.toLocaleDateString('en-US');
}

/**
 * Generate PDF using PDF.co API
 */
async function generatePdfWithPdfCo(formData) {
  console.log('🎯 Starting PDF generation with PDF.co...');

  return new Promise((resolve, reject) => {
    try {
      // Convert form data to PDF.co format
      const annotations = convertFormDataToPdfCoAnnotations(formData);

      // Prepare PDF.co API payload
      const payload = {
        url: TEMPLATE_PDF_URL,
        name: "Florida-Purchase-Agreement-Filled",
        async: false,
        inline: false,
        annotations: annotations
      };

      const jsonPayload = JSON.stringify(payload);

      // PDF.co API request options
      const options = {
        hostname: 'api.pdf.co',
        port: 443,
        path: '/v1/pdf/edit/add',
        method: 'POST',
        headers: {
          'x-api-key': PDF_CO_API_KEY,
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(jsonPayload)
        }
      };

      console.log('🚀 Sending request to PDF.co API...');

      const req = https.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            
            if (response.error === false) {
              console.log('✅ PDF.co API success');
              console.log('📄 Generated PDF URL:', response.url);
              
              // Download the PDF from PDF.co
              downloadPdfFromUrl(response.url)
                .then(pdfBuffer => {
                  resolve({
                    success: true,
                    pdfBuffer: pdfBuffer,
                    url: response.url,
                    pageCount: response.pageCount || 1
                  });
                })
                .catch(downloadError => {
                  console.error('❌ PDF download failed:', downloadError);
                  reject(downloadError);
                });
            } else {
              console.error('❌ PDF.co API error:', response.message);
              reject(new Error(response.message || 'PDF.co API error'));
            }
          } catch (parseError) {
            console.error('❌ Failed to parse PDF.co response:', parseError);
            reject(parseError);
          }
        });
      });

      req.on('error', (error) => {
        console.error('❌ PDF.co API request failed:', error);
        reject(error);
      });

      // Send the request
      req.write(jsonPayload);
      req.end();

    } catch (error) {
      console.error('❌ PDF generation setup failed:', error);
      reject(error);
    }
  });
}

/**
 * Download PDF from URL
 */
function downloadPdfFromUrl(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      if (res.statusCode !== 200) {
        reject(new Error(`Failed to download PDF: ${res.statusCode}`));
        return;
      }

      const chunks = [];
      res.on('data', (chunk) => chunks.push(chunk));
      res.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        console.log('✅ PDF downloaded successfully, size:', pdfBuffer.length, 'bytes');
        resolve(pdfBuffer);
      });
    }).on('error', reject);
  });
}

// API Endpoints

/**
 * Generate PDF - Main endpoint using PDF.co
 */
app.post('/generate-pdf', async (req, res) => {
  console.log('\n🎯 === PDF.CO GENERATION REQUEST ===');
  console.log('📊 Form data received:', Object.keys(req.body).length, 'fields');

  try {
    const result = await generatePdfWithPdfCo(req.body);

    if (result.success) {
      console.log('✅ PDF generation successful');
      
      // Set headers for PDF download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="Florida-Purchase-Agreement.pdf"');
      res.setHeader('Content-Length', result.pdfBuffer.length);
      
      // Send PDF
      res.send(result.pdfBuffer);
      
      console.log('📤 PDF sent to client');
    } else {
      console.error('❌ PDF generation failed');
      res.status(500).json({
        error: 'PDF generation failed',
        details: 'Unknown error occurred'
      });
    }

  } catch (error) {
    console.error('❌ Server error:', error);
    res.status(500).json({
      error: 'PDF generation failed',
      details: error.message
    });
  }
});

/**
 * Test PDF.co API connection
 */
app.get('/test-pdfco', async (req, res) => {
  console.log('\n🧪 === TESTING PDF.CO API CONNECTION ===');

  try {
    // Simple test with minimal data
    const testData = {
      sellerName: "TEST SELLER",
      buyerName: "TEST BUYER",
      purchasePrice: 100000
    };

    const result = await generatePdfWithPdfCo(testData);
    
    res.json({
      success: true,
      message: 'PDF.co API connection successful',
      pdfSize: result.pdfBuffer.length,
      url: result.url
    });

  } catch (error) {
    console.error('❌ PDF.co test failed:', error);
    res.status(500).json({
      success: false,
      error: 'PDF.co API test failed',
      details: error.message
    });
  }
});

/**
 * Health check
 */
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'PDF.co Generator',
    timestamp: new Date().toISOString(),
    apiKey: PDF_CO_API_KEY ? 'Configured' : 'Missing',
    templateUrl: TEMPLATE_PDF_URL
  });
});

// Start server
app.listen(PORT, () => {
  console.log('\n🚀 === PDF.CO GENERATOR STARTED ===');
  console.log(`📡 Server running on port ${PORT}`);
  console.log(`🎯 Generate PDF: POST http://localhost:${PORT}/generate-pdf`);
  console.log(`🧪 Test PDF.co: GET http://localhost:${PORT}/test-pdfco`);
  console.log(`❤️ Health Check: GET http://localhost:${PORT}/health`);
  console.log(`🔑 API Key: ${PDF_CO_API_KEY ? 'Configured' : '❌ MISSING - Set PDF_CO_API_KEY environment variable'}`);
  console.log(`📄 Template: ${TEMPLATE_PDF_URL}`);
  console.log('✅ Ready to generate perfect PDFs with PDF.co!\n');
});

module.exports = { generatePdfWithPdfCo, convertFormDataToPdfCoAnnotations };
