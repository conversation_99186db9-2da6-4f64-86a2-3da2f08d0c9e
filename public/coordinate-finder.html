<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Coordinate Finder - Florida Purchase Agreement</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .header h1 {
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .field-mapping {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .field-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        
        .field-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        
        .field-name {
            font-weight: bold;
            color: #333;
        }
        
        .coordinates {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .code-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
            margin-top: 20px;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .copy-btn:hover {
            background: #218838;
        }
        
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .note strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 PDF Coordinate Finder</h1>
            <p>Find exact coordinates for your Florida Purchase Agreement PDF fields</p>
        </div>

        <div class="instructions">
            <h3>📋 How to Use This Tool:</h3>
            <ol>
                <li><strong>Get PDF.co API Key:</strong> Sign up at <a href="https://app.pdf.co" target="_blank">app.pdf.co</a></li>
                <li><strong>Use PDF.co Helper:</strong> Go to <a href="https://app.pdf.co/pdf-edit-add-helper" target="_blank">PDF Edit Add Helper</a></li>
                <li><strong>Upload Your PDF:</strong> Upload <code>Florida-As_Is-Purchase-Agreement.pdf</code></li>
                <li><strong>Click on Fields:</strong> Click where each field should be placed</li>
                <li><strong>Copy Coordinates:</strong> Note the X,Y coordinates for each field</li>
                <li><strong>Update Code:</strong> Use the coordinates in your server code</li>
            </ol>
        </div>

        <div class="field-mapping">
            <div class="field-list">
                <h3>🏠 Required Fields</h3>
                
                <div class="field-item">
                    <span class="field-name">Seller Name</span>
                    <span class="coordinates">x: 150, y: 700</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Buyer Name</span>
                    <span class="coordinates">x: 150, y: 680</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Property Address</span>
                    <span class="coordinates">x: 200, y: 650</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">County</span>
                    <span class="coordinates">x: 150, y: 630</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Tax ID</span>
                    <span class="coordinates">x: 350, y: 630</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Purchase Price</span>
                    <span class="coordinates">x: 400, y: 500</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Initial Deposit</span>
                    <span class="coordinates">x: 400, y: 480</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Closing Date</span>
                    <span class="coordinates">x: 200, y: 400</span>
                </div>
            </div>

            <div class="field-list">
                <h3>☑️ Checkboxes</h3>
                
                <div class="field-item">
                    <span class="field-name">Cash Purchase</span>
                    <span class="coordinates">x: 100, y: 470</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Financing Required</span>
                    <span class="coordinates">x: 100, y: 450</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Conventional Loan</span>
                    <span class="coordinates">x: 150, y: 430</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">FHA Loan</span>
                    <span class="coordinates">x: 200, y: 430</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">VA Loan</span>
                    <span class="coordinates">x: 250, y: 430</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Seller Pays Closing</span>
                    <span class="coordinates">x: 100, y: 350</span>
                </div>
                
                <div class="field-item">
                    <span class="field-name">Buyer Pays Closing</span>
                    <span class="coordinates">x: 100, y: 330</span>
                </div>
            </div>
        </div>

        <div class="code-output">
            <h3>📝 Generated Code (Copy to server/pdfCoGenerator.cjs):</h3>
            <pre id="generatedCode">
// Update the annotations array in convertFormDataToPdfCoAnnotations function
const annotations = [
  // Seller Information
  {
    text: formData.sellerName || '',
    x: 150,  // ← Update with actual coordinates from PDF.co helper
    y: 700,  // ← Update with actual coordinates from PDF.co helper
    size: 12,
    pages: "0",
    fontName: "Times New Roman",
    type: "Text"
  },
  
  // Buyer Information  
  {
    text: formData.buyerName || '',
    x: 150,  // ← Update with actual coordinates
    y: 680,  // ← Update with actual coordinates
    size: 12,
    pages: "0",
    fontName: "Times New Roman",
    type: "Text"
  },
  
  // Purchase Price
  {
    text: formatCurrency(formData.purchasePrice),
    x: 400,  // ← Update with actual coordinates
    y: 500,  // ← Update with actual coordinates
    size: 12,
    pages: "0",
    fontName: "Times New Roman",
    fontBold: true,
    type: "Text"
  }
  
  // Add more fields as needed...
];
            </pre>
            <button class="copy-btn" onclick="copyCode()">📋 Copy Code</button>
        </div>

        <div class="note">
            <strong>⚠️ Important Notes:</strong>
            <ul>
                <li>PDF coordinates start from <strong>bottom-left corner</strong> (0,0)</li>
                <li>A4 page size is approximately <strong>595 x 842 points</strong></li>
                <li>1 inch = 72 points</li>
                <li>Use the <a href="https://app.pdf.co/pdf-edit-add-helper" target="_blank">PDF.co Helper Tool</a> for exact coordinates</li>
                <li>Test with a few fields first, then add more gradually</li>
            </ul>
        </div>
    </div>

    <script>
        function copyCode() {
            const codeElement = document.getElementById('generatedCode');
            const textArea = document.createElement('textarea');
            textArea.value = codeElement.textContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            const button = document.querySelector('.copy-btn');
            const originalText = button.textContent;
            button.textContent = '✅ Copied!';
            button.style.background = '#28a745';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#28a745';
            }, 2000);
        }
    </script>
</body>
</html>
