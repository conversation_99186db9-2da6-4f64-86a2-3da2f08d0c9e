#!/usr/bin/env node

/**
 * Test script for PDF.co integration
 * Run this to verify your PDF.co setup is working
 */

const https = require('https');

// Configuration
const PDF_CO_API_KEY = process.env.PDF_CO_API_KEY || 'YOUR_API_KEY_HERE';
const TEMPLATE_PDF_URL = 'https://pdfco-test-files.s3.us-west-2.amazonaws.com/pdf-form/f1040.pdf'; // Test PDF

console.log('🧪 Testing PDF.co Integration...\n');

// Test data
const testAnnotations = [
  {
    text: "TEST SELLER NAME",
    x: 100,
    y: 700,
    size: 12,
    pages: "0",
    fontName: "Times New Roman",
    type: "Text"
  },
  {
    text: "TEST BUYER NAME", 
    x: 100,
    y: 680,
    size: 12,
    pages: "0",
    fontName: "Times New Roman",
    type: "Text"
  },
  {
    text: "$500,000",
    x: 100,
    y: 650,
    size: 14,
    pages: "0",
    fontName: "Times New Roman",
    fontBold: true,
    type: "Text"
  }
];

// Test payload
const payload = {
  url: TEMPLATE_PDF_URL,
  name: "test-florida-agreement",
  async: false,
  inline: false,
  annotations: testAnnotations
};

const jsonPayload = JSON.stringify(payload);

// API request options
const options = {
  hostname: 'api.pdf.co',
  port: 443,
  path: '/v1/pdf/edit/add',
  method: 'POST',
  headers: {
    'x-api-key': PDF_CO_API_KEY,
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(jsonPayload)
  }
};

console.log('📡 Sending test request to PDF.co...');
console.log('🔑 API Key:', PDF_CO_API_KEY.substring(0, 8) + '...');
console.log('📄 Template URL:', TEMPLATE_PDF_URL);
console.log('📊 Annotations:', testAnnotations.length);

const req = https.request(options, (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      
      console.log('\n📥 Response received:');
      console.log('Status Code:', res.statusCode);
      
      if (response.error === false) {
        console.log('✅ SUCCESS! PDF.co integration working');
        console.log('📄 Generated PDF URL:', response.url);
        console.log('📊 Page Count:', response.pageCount);
        console.log('💰 Credits Used:', response.credits);
        console.log('💳 Remaining Credits:', response.remainingCredits);
        
        console.log('\n🎉 PDF.co Test Results:');
        console.log('✅ API Connection: Working');
        console.log('✅ Authentication: Valid');
        console.log('✅ PDF Generation: Successful');
        console.log('✅ Field Placement: Ready for customization');
        
        console.log('\n🚀 Next Steps:');
        console.log('1. Get your Florida PDF template URL');
        console.log('2. Use PDF.co helper tool to find exact coordinates');
        console.log('3. Update coordinates in server/pdfCoGenerator.cjs');
        console.log('4. Test with your actual form data');
        
      } else {
        console.log('❌ ERROR: PDF.co API returned error');
        console.log('Message:', response.message);
        
        if (response.message && response.message.includes('API key')) {
          console.log('\n🔧 Fix: Set your PDF.co API key:');
          console.log('export PDF_CO_API_KEY="your_actual_api_key"');
        }
      }
    } catch (parseError) {
      console.error('❌ Failed to parse response:', parseError);
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error.message);
  
  if (error.code === 'ENOTFOUND') {
    console.log('\n🔧 Fix: Check your internet connection');
  } else if (error.code === 'ECONNREFUSED') {
    console.log('\n🔧 Fix: PDF.co API might be down, try again later');
  }
});

// Send the request
req.write(jsonPayload);
req.end();

// Timeout after 30 seconds
setTimeout(() => {
  console.log('⏰ Request timeout - PDF.co might be slow, try again');
  process.exit(1);
}, 30000);
